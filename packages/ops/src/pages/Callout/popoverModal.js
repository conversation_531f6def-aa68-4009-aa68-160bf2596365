import React, { useState, useEffect, useCallback } from 'react';
import Typography from '@mui/material/Typography';
import { Button, Modal, Box, TextField } from '@mui/material';

const PopoverModal = ({
  title,
  open,
  onClose,
  btnText,
  onSubmit,
  placeholder,
  defaultValue,
  reset,
}) => {
  const [text, setText] = useState(defaultValue);

  useEffect(() => {
    if (reset) {
      setText('');
    } else if (defaultValue) {
      setText(defaultValue);
    } else {
      setText('');
    }
  }, [reset, defaultValue]);

  const handleSubmit = useCallback(() => {
    if (onSubmit) {
      onSubmit(text);
    }
  }, [onSubmit, text]);

  const handleModalClose = () => {
    if (defaultValue) {
      setText(defaultValue);
    } else {
      setText('');
    }
    onClose();
  };

  return (
    <Modal
      open={open}
      onClose={onClose}
      aria-labelledby="modal-title"
      aria-describedby="modal-description"
    >
      <Box
        sx={{
          position: 'absolute',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          width: 800,
          bgcolor: 'background.paper',
          boxShadow: 24,
          p: 2,
        }}
      >
        <Typography variant="subtitle1" className="fw-700 mb-16">
          {title}
        </Typography>
        <TextField
          fullWidth
          multiline
          rows={8}
          variant="outlined"
          onChange={(e) => setText(e.target.value)}
          placeholder={placeholder}
          sx={{ marginBottom: 2 }}
          defaultValue={text || defaultValue}
          value={text}
        />
        <Box
          direction="row"
          spacing={2}
          sx={{ justifyContent: 'end' }}
          display="flex"
          className="justify-content-end"
        >
          <Button
            variant="outlined"
            onClick={handleModalClose}
            className="mr-12"
            sx={{ mr: 2 }}
          >
            Cancel
          </Button>
          <Button variant="contained" color="primary" onClick={handleSubmit}>
            {btnText}
          </Button>
        </Box>
      </Box>
    </Modal>
  );
};

export default PopoverModal;
