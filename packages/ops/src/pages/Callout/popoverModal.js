import React, { useState, useEffect, useCallback } from 'react';
import Typography from '@mui/material/Typography';
import { Button, Modal, Box, TextField } from '@mui/material';
import moment from 'moment';

const PopoverModal = ({
  title,
  open,
  onClose,
  btnText,
  onSubmit,
  placeholder,
  defaultValue,
  reset,
  previousFeedback, // This will now be an array of feedback history
}) => {
  const [text, setText] = useState(defaultValue);

  useEffect(() => {
    if (reset) {
      setText('');
    } else if (defaultValue) {
      setText(defaultValue);
    } else {
      setText('');
    }
  }, [reset, defaultValue]);

  const handleSubmit = useCallback(() => {
    if (onSubmit) {
      onSubmit(text);
    }
  }, [onSubmit, text]);

  const handleModalClose = () => {
    if (defaultValue) {
      setText(defaultValue);
    } else {
      setText('');
    }
    onClose();
  };

  return (
    <Modal
      open={open}
      onClose={onClose}
      aria-labelledby="modal-title"
      aria-describedby="modal-description"
    >
      <Box
        sx={{
          position: 'absolute',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          width: 800,
          bgcolor: 'background.paper',
          boxShadow: 24,
          p: 2,
        }}
      >
        {/* Fixed Header */}
        <Typography
          variant="subtitle1"
          className="fw-700 mb-3"
          sx={{ flexShrink: 0 }}
        >
          {title}
        </Typography>

        {previousFeedback &&
          Array.isArray(previousFeedback) &&
          previousFeedback.length > 0 && (
            <Box sx={{ marginBottom: 3 }}>
              <Typography
                variant="subtitle1"
                sx={{
                  fontWeight: 700,
                  fontSize: '14px',
                  marginBottom: 2,
                  color: '#000000',
                  fontFamily: 'robotoRegular',
                }}
              >
                Previous feedback sent
              </Typography>
              <Box
                sx={{
                  maxHeight: 200, // Cap height to avoid expanding modal
                  overflowY: 'auto', // Enable vertical scroll
                  paddingRight: 1,
                }}
              >
                {previousFeedback.map((feedbackEntry, index) => (
                  <Box
                    key={feedbackEntry._id || index}
                    sx={{
                      padding: 2,
                      marginBottom: 2,
                      borderRadius: 1,
                      backgroundColor: '#f5f5f5',
                    }}
                  >
                    <Typography
                      variant="body2"
                      sx={{
                        fontWeight: 700,
                        fontSize: '14px',
                        marginBottom: 1,
                        color: '#000',
                        fontFamily: 'robotoRegular',
                      }}
                    >
                      {feedbackEntry.addedAt
                        ? moment(
                            feedbackEntry.addedAt,
                            ['DD/MM/YYYY HH:mm:ss', moment.ISO_8601],
                            true
                          ).isValid()
                          ? moment(feedbackEntry.addedAt, [
                              'DD/MM/YYYY HH:mm:ss',
                              moment.ISO_8601,
                            ]).format('DD MMM YYYY HH:mm')
                          : 'Invalid date'
                        : 'Date not available'}
                    </Typography>
                    <Typography
                      variant="body2"
                      sx={{
                        whiteSpace: 'pre-wrap',
                        fontWeight: 400,
                        fontSize: '14px',
                        color: '#000000',
                        fontFamily: 'robotoRegular',
                      }}
                    >
                      {feedbackEntry.feedback}
                    </Typography>
                  </Box>
                ))}
              </Box>
            </Box>
          )}

        <TextField
          fullWidth
          multiline
          rows={8}
          variant="outlined"
          onChange={(e) => setText(e.target.value)}
          placeholder={placeholder}
          sx={{ marginBottom: 2 }}
          defaultValue={text || defaultValue}
          value={text}
        />
        <Box
          direction="row"
          spacing={2}
          sx={{ justifyContent: 'end' }}
          display="flex"
          className="justify-content-end"
        >
          <Button
            variant="outlined"
            onClick={handleModalClose}
            className="mr-12"
            sx={{ mr: 2 }}
          >
            Cancel
          </Button>
          <Button variant="contained" color="primary" onClick={handleSubmit}>
            {btnText}
          </Button>
        </Box>
      </Box>
    </Modal>
  );
};

export default PopoverModal;
