import { useEffect } from 'react';
import { includes, get } from 'lodash';
import { useStore } from 'react-redux';
import { useRouter } from 'next/router';
import { setToken } from 'reducer/auth';
import { jwtDecode } from 'jwt-decode';
import moment from 'moment';

const Index = (WrappedComponent, displayName) => {
  const ComponentWithAuth = (props) => {
    const store = useStore();
    const router = useRouter();
    const { token, cachedRoute, userJourney, userData, snapRedirectUrl } =
      store.getState().auth;
    const { backButton } = store.getState().user;
    // const { projectCount } = store.getState().project;

    // Handle reload and redirect to last visited page
    const routeHandler = (path) => {
      if (router.pathname !== path) {
        router.push(path);
      }
      return true;
    };

    /**
     * This method is responsible to check authentication
     *
     * @param {any} token - Auth0 jwt token
     * @returns {boolean} - true/false
     */
    const isAuthenticated = async (token) => {
      const PublicUrls = process.env.PublicUrls || [];

      if (!token && !includes(PublicUrls, router.pathname)) {
        await router.push('/');
        return false;
      }

      // When token is expire redirect and logout
      if (token) {
        // Logout user if token is expired
        const decoded = jwtDecode(token);
        if (moment().unix() > decoded.exp) {
          store.dispatch(setToken(''));
          store.__persistor.purge();
          localStorage.clear();
          // eslint-disable-next-line no-alert
          alert(
            'Oops! Looks like your session has expired, Redirecting to login page.',
          );
          await router.replace('/');
          window.location.reload();
          return false;
        }

        if (cachedRoute) {
          return routeHandler(cachedRoute);
        }

        // Redirect user to dashboard/last visited page if user is already logged in
        const needToValidate = [
          '/',
          '/signIn',
          '/signUp',
          '/discoverer/signUp',
          '/collaborator/signUp',
          '/profile/organization',
          '/profile/getStarted',
          '/discoverer/profile/organization',
          '/discoverer/profile/getStarted',
        ];
        if (includes(needToValidate, router.pathname)) {
          const redirectUrlDm = get(
            userData,
            'userMeta.redirectUrlDecisionMaker',
            false,
          );
          const dmSigIn =
            redirectUrlDm && redirectUrlDm.includes('decisionMaker');
          const { name, organisation } = get(userData, 'profile', {});

          if (name && snapRedirectUrl) {
            return routeHandler(snapRedirectUrl);
          }

          if (
            !backButton &&
            (userJourney === 'decisionMakerSignUp' || dmSigIn)
          ) {
            if (organisation) {
              return routeHandler(`/discoverer/dashboard`);
            }
            if (name) {
              return routeHandler(`/discoverer/profile/organization`);
            }
            if (!name) {
              return routeHandler(`/discoverer/profile/getStarted`);
            }
          }

          if (!redirectUrlDm && !backButton) {
            // Get user profile data to check onboarding completion
            const { organisation } = get(userData, 'profile', {});

            // Step-by-step onboarding flow
            // 1. If no name, redirect to getStarted
            if (!name) {
              return routeHandler(`/profile/getStarted`);
            }

            // 2. If name exists but no organization, redirect to organization
            if (name && !organisation) {
              return routeHandler(`/profile/organization`);
            }

            // 3. If both name and organization exist, user has completed onboarding
            // Redirect to mydashboard
            if (name && organisation) {
              return routeHandler(`/mydashboard`);
            }

            // Fallback: if name exists, go to mydashboard
            if (name) {
              return routeHandler(`/mydashboard`);
            }
          }
        }
      }
      return true;
    };

    useEffect(() => {
      (async function () {
        await isAuthenticated(token);
      })();
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [token, router]);

    return <WrappedComponent {...props} />;
  };

  ComponentWithAuth.displayName =
    displayName ||
    `withAuth(${WrappedComponent.displayName || WrappedComponent.name})`;

  return ComponentWithAuth;
};

export default Index;
