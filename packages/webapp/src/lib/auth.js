import { useEffect } from 'react';
import { includes, get } from 'lodash';
import { useStore } from 'react-redux';
import { useRouter } from 'next/router';
import { setToken } from 'reducer/auth';
import { jwtDecode } from 'jwt-decode';
import moment from 'moment';

const Index = (WrappedComponent, displayName) => {
  const ComponentWithAuth = (props) => {
    const store = useStore();
    const router = useRouter();
    const { token, cachedRoute, userJourney, userData, snapRedirectUrl } =
      store.getState().auth;
    const { backButton } = store.getState().user;
    const { projectCount } = store.getState().project;

    // Handle reload and redirect to last visited page
    const routeHandler = (path) => {
      if (router.pathname !== path) {
        router.push(path);
      }
      return true;
    };

    /**
     * This method is responsible to check authentication
     *
     * @param {any} token - Auth0 jwt token
     * @returns {boolean} - true/false
     */
    const isAuthenticated = async (token) => {
      const PublicUrls = process.env.PublicUrls || [];

      if (!token && !includes(PublicUrls, router.pathname)) {
        await router.push('/');
        return false;
      }

      // When token is expire redirect and logout
      if (token) {
        // Logout user if token is expired
        const decoded = jwtDecode(token);
        if (moment().unix() > decoded.exp) {
          store.dispatch(setToken(''));
          store.__persistor.purge();
          localStorage.clear();
          // eslint-disable-next-line no-alert
          alert(
            'Oops! Looks like your session has expired, Redirecting to login page.',
          );
          await router.replace('/');
          window.location.reload();
          return false;
        }

        if (cachedRoute) {
          return routeHandler(cachedRoute);
        }

        // Redirect user to dashboard/last visited page if user is already logged in
        const needToValidate = [
          '/',
          '/signIn',
          '/signUp',
          '/discoverer/signUp',
          '/collaborator/signUp',
          '/profile/organization',
          '/profile/getStarted',
          '/discoverer/profile/organization',
          '/discoverer/profile/getStarted',
        ];
        if (includes(needToValidate, router.pathname)) {
          const redirectUrlDm = get(
            userData,
            'userMeta.redirectUrlDecisionMaker',
            false,
          );
          const dmSigIn =
            redirectUrlDm && redirectUrlDm.includes('decisionMaker');
          const { name, organisation } = get(userData, 'profile', {});
          console.log(name, 'name===========>');

          if (name && snapRedirectUrl) {
            return routeHandler(snapRedirectUrl);
          }

          if (
            !backButton &&
            (userJourney === 'decisionMakerSignUp' || dmSigIn)
          ) {
            if (organisation) {
              return routeHandler(`/discoverer/dashboard`);
            }
            if (name) {
              return routeHandler(`/discoverer/profile/organization`);
            }
            if (!name) {
              return routeHandler(`/discoverer/profile/getStarted`);
            }
          }

          if (!redirectUrlDm && !backButton) {
            // Get user profile data to check onboarding completion
            const { organisation } = get(userData, 'profile', {});
            const subscriptionType = get(userData, 'userMeta.type', null);

            // Step-by-step onboarding flow based on original commented logic
            // Only redirect to mydashboard if user has completed ALL onboarding steps
            if (name && projectCount > 0 && subscriptionType) {
              return routeHandler(`/mydashboard`);
            }
            // If user has name and organisation but no subscription, continue onboarding
            if (name && organisation && !subscriptionType) {
              return routeHandler(`/profile/addImage`);
            }
            // If user has name and organisation and subscription, go to whereToStart
            if (name && organisation && subscriptionType) {
              return routeHandler(`/profile/whereToStart`);
            }
            if (name) {
              return routeHandler(`/profile/organization`);
            }
            if (!name) {
              return routeHandler(`/profile/getStarted`);
            }
          }
        }
      }
      return true;
    };

    useEffect(() => {
      (async function () {
        await isAuthenticated(token);
      })();
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [token, router]);

    return <WrappedComponent {...props} />;
  };

  ComponentWithAuth.displayName =
    displayName ||
    `withAuth(${WrappedComponent.displayName || WrappedComponent.name})`;

  return ComponentWithAuth;
};

export default Index;
