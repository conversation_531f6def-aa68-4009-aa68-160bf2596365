import React from 'react';
import { connect } from 'react-redux';
import PropTypes from 'prop-types';
import _, { get } from 'lodash';
import Head from 'next/head';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import Header from 'sharedComponents/Header/dashboardHeader';
import { withRouter } from 'next/router';
import Loader from 'sharedComponents/loader';
import { setUserData, updateUserMeta } from 'reducer/auth';
import {
  toggleTheme,
  fetchCollaborator,
  updateCollaborator,
  updateOtherDocuments,
  fetchProjectDetailsByID,
  setCollaboratorInfoModalStatus,
} from 'reducer/project';
import {
  getPlans,
  getClientSecretKey,
  getSubscription,
} from 'reducer/subscription';
import { setLoadingStatus, updateProfilePicture } from 'reducer/user';
import AddProfile from './components/AddProfile';
import mixpanelAnalytics from 'lib/mixpanel';

// Container to render AddProfile Component
class Index extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      collaboratorProjectDetails: null,
    };
    const { setLoadingStatus } = this.props;
    // method to stop loader
    setLoadingStatus(false);
  }

  async componentDidMount() {
    const {
      toggleTheme,
      userJourney,
      collaboratorProjectId,
      userData,
      fetchCollaborator,
      updateCollaborator,
      updateOtherDocuments,
      fetchProjectDetailsByID,
      setCollaboratorInfoModalStatus,
      router,
      getPlans,
      getSubscription,
    } = this.props;

    await getPlans();
    toggleTheme('onBoarding');
    // Trigger Mixpanel event for page landing
    mixpanelAnalytics('add_image_page_opened', {
      pageName: 'add_image',
    });
    const currentPath = router.pathname;
    getSubscription(currentPath).then((subscription) => {
      if (get(subscription, 'userMeta.type', null)) {
        if (currentPath === '/profile/addImage') {
          router.push('/mydashboard');
        }
      }
    });
    let collaboratorData = null;
    const emailId = await _.get(userData, 'email');
    const userId = await _.get(userData, '_id');
    const firstName = await _.get(userData, 'profile.name.firstName', '');
    const lastName = await _.get(userData, 'profile.name.lastName', '');
    const name = firstName + lastName;
    let projectDetails = null;
    let acceptStatus = true;

    if (
      userJourney === 'collaboratorSignUp' &&
      collaboratorProjectId !== null &&
      collaboratorProjectId !== undefined
    ) {
      projectDetails = await fetchProjectDetailsByID(collaboratorProjectId);
      collaboratorData = await fetchCollaborator({
        email: encodeURIComponent(emailId),
        id: collaboratorProjectId,
      });

      const projectCollaboratorsList = _.get(
        projectDetails,
        'projectCollaborator',
        false,
      );
      if (projectCollaboratorsList && projectDetails) {
        projectCollaboratorsList.map((item) => {
          if (userId === item.id) {
            acceptStatus = false;
          }
        });
      }

      const data = collaboratorData ? collaboratorData[0] : false;
      this.setState({ collaboratorProjectDetails: data });
      const id = await _.get(data, '_id');
      if (data) {
        setCollaboratorInfoModalStatus(true);
        const value = {
          projectCreatorInfo: {
            projectCreatorId: data.projectCreatorInfo.projectCreatorId,
          },
          projectInfo: {
            projectsId: collaboratorProjectId,
            title: data.projectInfo.title,
            producer: data.projectInfo.producer,
            director: data.projectInfo.director,
            writer: data.projectInfo.writer,
          },
          status: 'accepted',
          email: data.email,
          profileImage: '',
          fullName: name,
          reminders: data.reminders,
        };

        const projectCollaboratorData = {
          email: data.email,
          status: 'accepted',
          id: userId,
        };
        if (acceptStatus) {
          await updateOtherDocuments(
            projectCollaboratorData,
            collaboratorProjectId,
            'projectCollaborator',
          );
        }
        updateCollaborator(value, id);
      }
    }
  }

  // Profile image post to api  handler
  uploadHandler = (values) => {
    const { updateProfilePicture } = this.props;
    // method to upload image in creator
    updateProfilePicture({
      profileImage: get(values, 'fileUploadIcon.url', ''),
      isDecisionMaker: false,
    });
    // Track Mixpanel event for profile update
    mixpanelAnalytics('onboarding_profile_picture_uploaded', {
      profileImage: get(values, 'fileUploadIcon.url', ''),
    });
  };

  goBack = () => {
    const { router } = this.props;
    // method to go back to getStarted page
    router.push('/profile/organization');
  };

  render() {
    const {
      userData,
      isLoading,
      getClientSecretKey,
      trialPlan,
      proPlan,
      enterPrisePlan,
    } = this.props;

    const { collaboratorProjectDetails } = this.state;
    return (
      <>
        <Head>
          <title>Getting Started | ActivateU</title>
          <link rel="shortcut icon" href="/favicon.ico" />
        </Head>

        {isLoading ? (
          <Loader />
        ) : (
          <>
            <Header
              noLogoLink
              noAvatar
              btnName="Back"
              clickHandler={this.goBack}
            />

            <div className="container-fluid backGroundImage themeContainer bg">
              <AddProfile
                userName={
                  userData.profile
                    ? `${get(userData, 'profile.name.firstName', '')} ${get(userData, 'profile.name.lastName', '')}`
                    : ''
                }
                uploadHandler={this.uploadHandler}
                updateUserMeta={updateUserMeta}
                collaboratorProjectDetails={collaboratorProjectDetails}
                getClientSecretKey={getClientSecretKey}
                proPlan={proPlan}
                enterPrisePlan={enterPrisePlan}
                trialPlan={trialPlan}
                {...this.props}
              />
            </div>
          </>
        )}
      </>
    );
  }
}

export async function getStaticProps({ locale }) {
  return {
    props: {
      ...(await serverSideTranslations(locale)),
    },
  };
}

Index.propTypes = {
  setUserData: PropTypes.func.isRequired,
  userData: PropTypes.object.isRequired,
  updateProfilePicture: PropTypes.func.isRequired,
  router: PropTypes.object.isRequired,
  isLoading: PropTypes.bool.isRequired,
  setLoadingStatus: PropTypes.func.isRequired,
  toggleTheme: PropTypes.func.isRequired,
  updateOtherDocuments: PropTypes.func.isRequired,
  updateCollaborator: PropTypes.func.isRequired,
  fetchCollaborator: PropTypes.func.isRequired,
  collaboratorProjectId: PropTypes.string.isRequired,
  userJourney: PropTypes.string.isRequired,
  fetchProjectDetailsByID: PropTypes.func.isRequired,
  setCollaboratorInfoModalStatus: PropTypes.func.isRequired,
};

const mapStateToProps = (state) => ({
  userData: state.auth.userData,
  isLoading: state.user.isLoading,
  projectCount: state.project.projectCount,
  userJourney: state.auth.userJourney,
  collaboratorProjectId: state.auth.collaboratorProjectId,
  allPlans: state.subscription.allPlans,
  trialPlan: state.subscription.trialPlan,
  proPlan: state.subscription.proPlan,
  enterPrisePlan: state.subscription.enterPrisePlan,
});
const mapDistpatchToProps = (dispatch) => {
  return {
    setUserData: (payload) => dispatch(setUserData(payload)),
    updateProfilePicture: (payload) => dispatch(updateProfilePicture(payload)),
    setLoadingStatus: (payload) => dispatch(setLoadingStatus(payload)),
    toggleTheme: (payload) => dispatch(toggleTheme(payload)),
    fetchCollaborator: (payload) => dispatch(fetchCollaborator(payload)),
    updateCollaborator: (payload, id) =>
      dispatch(updateCollaborator(payload, id)),
    updateOtherDocuments: (value, id, section) =>
      dispatch(updateOtherDocuments(value, id, section)),
    fetchProjectDetailsByID: (payload) =>
      dispatch(fetchProjectDetailsByID(payload)),
    setCollaboratorInfoModalStatus: (status) =>
      dispatch(setCollaboratorInfoModalStatus(status)),
    updateUserMeta: (payload) => dispatch(updateUserMeta(payload)),
    getPlans: () => dispatch(getPlans()),
    getSubscription: (payload) => dispatch(getSubscription(payload)),
    getClientSecretKey: (payload) => dispatch(getClientSecretKey(payload)),
  };
};
export default connect(mapStateToProps, mapDistpatchToProps)(withRouter(Index));
