import React, { Component } from 'react';
import { Field, reduxForm } from 'redux-form';
import { withRouter } from 'next/router';
import PropTypes from 'prop-types';
import { FormGroup } from 'react-bootstrap';
import RenderField from 'sharedComponents/renderfield';
import Button from 'sharedComponents/Button/button';
import OtpForm from 'sharedComponents/otp';
import { required, email } from 'validation/commonValidation';
import { withTranslation } from 'react-i18next';
import style from '../../signIn/styles/LoginForm.module.scss';
import ReactModal from 'sharedComponents/Modal/modal';
import mixpanelAnalytics from 'lib/mixpanel';

// Signup Component
class SignupForm extends Component {
  constructor(props) {
    super(props);
    this.state = {
      receiveMarketMaterial: false,
      agreeTermsPrivacy: false,
      isValue: false,
      acceptProjectNotifications: false,
      easyAccess: false,
      showModal: false,
      isOtpSent: false,
      otpNotVerified: false,
      userExist: false,
      isSubmitting: false,
    };
  }

  // Function to open the modal

  openModal = () => {
    this.setState({ showModal: true });
  };

  // Function to close the modal
  closeModal = () => {
    this.setState({ showModal: false });
  };

  // Submit signup data.
  submitEmailForm = async (values) => {
    const {
      agreeTermsPrivacy,
      acceptProjectNotifications,
      receiveMarketMaterial,
      easyAccess,
    } = this.state;
    const { setsignUpMeta, setUserJourney, startLoginWithOtp, isUserExist } =
      this.props;

    // Set user journey for analytics
    setUserJourney('filmMakerSignUp');

    if (this.state.isSubmitting) return; // Prevent multiple submissions

    // Set submitting state to true
    this.setState({ isSubmitting: true });

    const data = {
      userMeta: {
        receiveMarketMaterial,
        acceptProjectNotifications,
        easyAccessPermission: easyAccess,
        agreeTermsPrivacy,
        redirectUrl: '/profile/getStarted',
      },
    };

    const obj = {
      email: values.email.toLowerCase().trim(),
    };
    const existUserData = await isUserExist(obj);
    this.setState({ userExist: existUserData.data });
    if (agreeTermsPrivacy) {
      /* Handle user login OTP */
      startLoginWithOtp(obj)
        .then(() => {
          this.setState({ isOtpSent: true });

          // Track "OTP Sent" event
          mixpanelAnalytics('sign_up_otp_generate', {
            email: obj.email,
            pageName: 'sign_up',
          });
        })
        .catch((error) => {
          // Optionally track a failure event
          mixpanelAnalytics('SignUp_otp_verification_failure', {
            email: obj.email,
            error: error.message,
          });
        });

      setsignUpMeta(data);
    }
  };

  // Check email field value length.
  checkEmailField = (email) => {
    if (email.target.value.length > 0) {
      this.setState({ isValue: true });
    } else {
      this.setState({ isValue: false });
    }
  };
  // go to login
  gotologin = async () => {
    const { router, setUserJourney } = this.props;
    await setUserJourney('signIn');
    router.push('/');
  };

  /**
   * Verify user given otp
   * @param values
   */
  otpFormHandler = (values) => {
    const { verifyOtp, router } = this.props;
    verifyOtp(values.otp).then(async (response) => {
      if (response === false) {
        this.setState({ otpNotVerified: true });
      } else {
        router.push('/profile/getStarted');
      }
    });
  };

  onClickResendHandler = () => {
    const { startLoginWithOtp, savedEmail } = this.props;
    const obj = {
      email: savedEmail.toLowerCase().trim(),
    };
    startLoginWithOtp(obj).then(() => {
      this.setState({ isOtpSent: true });
    });
  };

  render() {
    const {
      agreeTermsPrivacy,
      acceptProjectNotifications,
      receiveMarketMaterial,
      isValue,
      // easyAccess,
      isOtpSent,
      otpNotVerified,
    } = this.state;
    const { handleSubmit, t, savedEmail } = this.props;
    const termsAndConditions = `
      <div style="font-family: maisonNeue; font-size: 14px; font-weight: 400">
      <p style="font-size: 14px">You are requesting to be provided with 'Easy Access' to certain Getty Images online images and video as part of the SMASH programme. By opting-in, you agree to the below terms in order for us to set you up with a user account for the Easy Access tool.</p>

      <p style="font-size: 14px">This confirms you acknowledge that the content is made available only for the purpose of including within pitches to potential commissioning parties within the confines of the SMASH platform. If you do wish to use any of the content within a final programme, film, or other end product, or wish to use it for any other purpose, internal or external, you’ll need to return and license it from Getty Images on commercial terms, prior to undertaking any further use.</p>

      <p style="font-size: 14px">Your Easy Access (EZA) account allows you to download content from the SMASH platform for the following uses in connection with the pitches: To create mood boards, treatments, sizzles, and trailers.</p>

      <p style="font-size: 14px">This overrides the standard online 'Comp licence' for still images and video found within the Getty Images Content Licence Agreement, available <a href="https://www.gettyimages.co.uk/eula" target="_blank" style="color: #1743D7 !important; text-decoration: none;">here</a>. The EZA account is not a license.</p>

      <p style="font-size: 14px">In order to finalize your project with any content you download from your EZA account, you need to purchase a license. Without a license, no further use can be made, such as:</p>

      <ul>
        <li>Pitching outside of the SMASH Early Access programme;</li>
        <li>Focus group presentations;</li>
        <li>External presentations;</li>
        <li>Final materials distributed inside your organization;</li>
        <li>Any materials distributed outside your organization; or</li>
        <li>Any materials distributed to the public (such as advertising, marketing).</li>
         <li>Because content collections are continually updated, Getty Images cannot guarantee that any particular item of content will be available until the time of licensing.</li>
      </ul>

      <p style="font-size: 14px">Please carefully review any restrictions accompanying the content on <a href="https://engage.gettyimages.com/gettyimages-x-smash#/en/partnership" target="_blank" style="color: #1743D7; text-decoration: none;">Getty Images</a> and contact us directly if you have a question about them.</p>

      <p style="font-size: 14px">Your EZA account is personal to you and may not be shared with any third party. You must keep all access information strictly confidential. Your EZA account will remain in place for three months; please let us know if you need longer access, for us to review.</p>
    </div>`;

    return (
      <div className="row justify-content-center mb-5 mt-5">
        <div className="col-12">
          <div className="modal-container p-md-5">
            <div className="row justify-content-center">
              <div className="col-12">
                <h3
                  data-cy="signUpHeading"
                  className="mb-3 text-center text-primary fw-400"
                >
                  {!this.state.userExist
                    ? t('common:home.signupForm.getYourStartupHeading')
                    : t('common:home.loginForm.signintoactivateu')}
                </h3>
                <p
                  data-cy="signUPSubHeading"
                  className="p2 mb-0 text-center text-primary m-0 p-0"
                  style={{
                    lineHeight: '21px',
                    wordWrap: 'break-word',
                  }}
                >
                  {t('common:home.signupForm.aboutSmash')}
                </p>
              </div>
            </div>

            <div className="row justify-content-center">
              <div className="col-12">
                <>
                  {!isOtpSent && (
                    <form
                      onSubmit={handleSubmit(this.submitEmailForm)}
                      className="mt-32"
                    >
                      <FormGroup name="formGroup" className="mb-3">
                        <label className="p2 text-primary">Email</label>
                        <Field
                          name="email"
                          component={RenderField}
                          validate={[required, email]}
                          type="email"
                          placeholder={t('common:home.signupForm.yourEmail')}
                          size="lg"
                          // onChange={(email) => this.checkEmailField(email)}
                          onChange={(event) => {
                            this.setState({ isSubmitting: false });
                            this.checkEmailField(event);
                          }}
                          onFocus={() => {
                            mixpanelAnalytics('sign_up_email_field_focused', {
                              fieldName: 'email',
                              pageName: 'sign_up',
                            });
                          }}
                        />
                      </FormGroup>
                      <div className="mb-2">
                        <div className="termsPrivacyValue">
                          <label className={style.container}>
                            <input
                              type="checkbox"
                              data-cy="tnc"
                              name="tnc"
                              disabled={false}
                              value="select"
                              checked={this.state.agreeTermsPrivacy}
                              onChange={() =>
                                this.setState({
                                  agreeTermsPrivacy: !agreeTermsPrivacy,
                                })
                              }
                            />
                            <span
                              className={`${style.checkmark} ${style.disableMark}`}
                            />
                          </label>
                          <p
                            className={`${style.iAgreeToReceiveE} mb-0 p3 text-primary`}
                            style={{
                              lineHeight: '18px',
                              wordWrap: 'break-word',
                            }}
                          >
                            {t('common:home.signupForm.byProceedingIAcc')}
                            <a
                              className={`${style.linkText}`}
                              href="https://mysmash.media/terms-of-service"
                              target="_blank"
                              rel="noopener noreferrer"
                            >
                              {t('common:home.signupForm.activateuterms')}
                            </a>
                            {t('common:home.signupForm.middletxt')}
                            <a
                              className={`${style.linkText}`}
                              href="https://mysmash.media/privacy-policy"
                              target="_blank"
                              rel="noopener noreferrer"
                            >
                              {t('common:home.signupForm.privacypolicy')}
                            </a>
                          </p>
                        </div>
                        <div className="projectNotifications">
                          <label className={style.container}>
                            <input
                              type="checkbox"
                              data-cy="projectNotification"
                              name="projectNotification"
                              disabled={false}
                              value="select"
                              onChange={() =>
                                this.setState({
                                  acceptProjectNotifications:
                                    !acceptProjectNotifications,
                                })
                              }
                            />
                            <span
                              className={`${style.checkmark} ${style.disableMark}`}
                            />
                          </label>
                          <p
                            className={`${style.iAgreeToReceiveE} p3 text-primary`}
                          >
                            {t(
                              'common:home.signupForm.iAgreeToReceiveProjectNotification',
                            )}
                          </p>
                        </div>{' '}
                        <label className={style.container}>
                          <input
                            type="checkbox"
                            data-cy="receiveEmail"
                            name="receiveEmail"
                            disabled={false}
                            value="select"
                            onChange={() =>
                              this.setState({
                                receiveMarketMaterial: !receiveMarketMaterial,
                              })
                            }
                          />
                          <span
                            className={`${style.checkmark} ${style.disableMark}`}
                          />
                        </label>
                        <p
                          className={`${style.iAgreeToReceiveE} p3 text-primary`}
                        >
                          {t(
                            'common:home.signupForm.iAgreeToReceiveMarketingMaterial',
                          )}
                        </p>
                        <ReactModal
                          isShowCrossBtn
                          title="Getty Easy Access Programme"
                          htmlBody={
                            <div
                              dangerouslySetInnerHTML={{
                                __html: termsAndConditions,
                              }}
                            />
                          }
                          modalShow={this.state.showModal}
                          closeCallback={this.closeModal}
                          modalSize="xl"
                          bodyClass="text-left"
                          titleClass={style.signUpFormTitle}
                          svgIconClass={style.svgIconStyle}
                          className="modalContentStyle"
                        />
                        <Button
                          btntype="submit"
                          id="submitBtn"
                          size="large"
                          isActive={agreeTermsPrivacy}
                          disabled={this.state.isSubmitting}
                          className="mt-3 w-100"
                          customClass={
                            agreeTermsPrivacy && isValue === true
                              ? 'modalBtn'
                              : '--disabled'
                          }
                          buttonValue={t('common:home.signupForm.getStarted')}
                          data-cy="signUpButton"
                        />
                      </div>
                    </form>
                  )}

                  {isOtpSent && (
                    <OtpForm
                      savedEmail={savedEmail}
                      formSubmitHandler={(otp) => this.otpFormHandler(otp)}
                      onClickResend={this.onClickResendHandler}
                      otpNotVerified={otpNotVerified}
                    />
                  )}
                </>
              </div>
            </div>

            <div className="row justify-content-center">
              <div className="col-12">
                <p
                  data-cy="goToSign"
                  className="p3 text-center text-primary fs-400 my-8"
                  style={{
                    lineHeight: '18px',
                  }}
                >
                  {t('common:home.signupForm.alreadyhaveaccount')}{' '}
                  <button
                    type="button"
                    id="sign-in"
                    className={`${style.linkbtn} p3`}
                    onClick={this.gotologin}
                  >
                    {t('common:home.signupForm.signinhere')}
                  </button>
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }
}

SignupForm.propTypes = {
  startLogin: PropTypes.func.isRequired,
  handleSubmit: PropTypes.func.isRequired,
  router: PropTypes.object.isRequired,
  t: PropTypes.func.isRequired,
  setsignUpMeta: PropTypes.func.isRequired,
  setUserJourney: PropTypes.func.isRequired,
};

export default withRouter(
  reduxForm({
    form: 'SignupForm',
  })(withTranslation('common')(SignupForm)),
);
